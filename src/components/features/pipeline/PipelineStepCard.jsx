/**
 * PipelineStepCard - Individual step component for the multi-step pipeline
 * Displays step status, progress, and action buttons
 */

import React, { useState } from 'react';
import { getStepColors, ACTION_TYPES, STEP_STATUS } from '../../../core/config/pipelineSteps.js';

const PipelineStepCard = ({
  step,
  status = STEP_STATUS.PENDING,
  progress = 0,
  result = null,
  error = null,
  timing = 0,
  onAction,
  isActive = false,
  showDetails = false,
  showExpandableData = false,
  compactMode = false // New prop for compact display
}) => {
  const [expanded, setExpanded] = useState(showDetails);
  const [showRawInput, setShowRawInput] = useState(false);
  const [showRawOutput, setShowRawOutput] = useState(false);
  const colors = getStepColors(step.color);

  const getStatusIcon = () => {
    switch (status) {
      case STEP_STATUS.COMPLETED:
        return '✅';
      case STEP_STATUS.RUNNING:
        return '⏳';
      case STEP_STATUS.ERROR:
        return '❌';
      case STEP_STATUS.SKIPPED:
        return '⏭️';
      default:
        return '⏸️';
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case STEP_STATUS.COMPLETED:
        return 'text-green-600';
      case STEP_STATUS.RUNNING:
        return 'text-blue-600';
      case STEP_STATUS.ERROR:
        return 'text-red-600';
      case STEP_STATUS.SKIPPED:
        return 'text-gray-500';
      default:
        return 'text-gray-400';
    }
  };

  const handleAction = (actionType) => {
    // Handle fold/unfold actions locally
    if (actionType === ACTION_TYPES.VIEW_RAW) {
      setShowRawInput(!showRawInput);
      return;
    }
    if (actionType === ACTION_TYPES.VIEW_OUTPUT) {
      setShowRawOutput(!showRawOutput);
      return;
    }

    // Pass other actions to parent
    if (onAction) {
      onAction(step.id, actionType);
    }
  };

  const formatTiming = (ms) => {
    if (ms < 1000) return `${ms}ms`;
    return `${(ms / 1000).toFixed(1)}s`;
  };

  const getActionButtons = () => {
    const buttons = [];

    step.actions.forEach(action => {
      let label, variant, disabled;

      switch (action) {
        case ACTION_TYPES.RERUN:
          label = '🔄 Rerun';
          variant = 'secondary';
          disabled = status === STEP_STATUS.RUNNING;
          break;
        case ACTION_TYPES.VIEW_RAW:
          label = showRawInput ? '📄 Hide Input' : '📄 Show Input';
          variant = 'outline';
          disabled = !result;
          break;
        case ACTION_TYPES.VIEW_OUTPUT:
          label = showRawOutput ? '📊 Hide Output' : '📊 Show Output';
          variant = 'outline';
          disabled = !result;
          break;
        case ACTION_TYPES.ENHANCE_PROMPT:
          label = '✨ Enhance';
          variant = 'primary';
          disabled = status !== STEP_STATUS.COMPLETED;
          break;
        case ACTION_TYPES.VIEW_SIMILAR:
          label = '🔗 Similar';
          variant = 'outline';
          disabled = !result?.similar_docs;
          break;
        case ACTION_TYPES.COMPARE_PDF:
          label = '🔍 Compare';
          variant = 'outline';
          disabled = !result;
          break;
        case ACTION_TYPES.VIEW_MAPPING:
          label = '🗺️ Mapping';
          variant = 'outline';
          disabled = !result?.mapped_fields;
          break;
        case ACTION_TYPES.VIEW_ERRORS:
          label = '⚠️ Errors';
          variant = 'outline';
          disabled = !result?.errors?.length;
          break;
        case ACTION_TYPES.EXPORT:
          label = '💾 Export';
          variant = 'primary';
          disabled = status !== STEP_STATUS.COMPLETED;
          break;
        case ACTION_TYPES.SAVE:
          label = '💾 Save';
          variant = 'primary';
          disabled = status !== STEP_STATUS.COMPLETED;
          break;
        default:
          return;
      }

      buttons.push(
        <button
          key={action}
          onClick={() => handleAction(action)}
          disabled={disabled}
          className={`px-2 py-1 text-xs rounded transition-colors ${
            variant === 'primary'
              ? 'bg-blue-600 text-white hover:bg-blue-700 disabled:bg-gray-300'
              : variant === 'secondary'
              ? 'bg-gray-600 text-white hover:bg-gray-700 disabled:bg-gray-300'
              : 'border border-gray-300 text-gray-700 hover:bg-gray-50 disabled:text-gray-400'
          }`}
        >
          {label}
        </button>
      );
    });

    return buttons;
  };

  // Render compact mode for better UX
  if (compactMode) {
    return (
      <div className={`
        relative p-3 rounded-lg border transition-all duration-200 hover:shadow-sm
        ${colors.bg} ${colors.border}
        ${isActive ? 'ring-2 ring-blue-500 ring-opacity-50 shadow-md' : ''}
        ${status === STEP_STATUS.ERROR ? 'border-red-300 bg-red-50' : ''}
      `}>
        {/* Compact Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <span className={`text-base ${colors.icon}`}>{step.icon}</span>
            <div className="flex-1 min-w-0">
              <h3 className={`font-medium text-sm ${colors.text} truncate`}>{step.name}</h3>
              {status === STEP_STATUS.RUNNING && (
                <div className="flex items-center space-x-2 mt-1">
                  <div className="w-16 bg-gray-200 rounded-full h-1">
                    <div
                      className={`h-1 rounded-full transition-all duration-300 ${colors.progress}`}
                      style={{ width: `${typeof progress === 'number' ? progress : 0}%` }}
                    />
                  </div>
                  <span className="text-xs text-gray-500">{typeof progress === 'number' ? progress : 0}%</span>
                </div>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <span className={`text-sm ${getStatusColor()}`}>{getStatusIcon()}</span>
            {(result || error) && (
              <button
                onClick={() => setExpanded(!expanded)}
                className="text-xs text-gray-500 hover:text-gray-700 p-1 rounded hover:bg-gray-100"
                title="View details"
              >
                {expanded ? '▼' : '▶'}
              </button>
            )}
          </div>
        </div>

        {/* Compact Status */}
        <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
          <span>
            {timing > 0 ? `${formatTiming(timing)}` :
             status === STEP_STATUS.RUNNING ? 'Processing...' :
             status === STEP_STATUS.COMPLETED ? 'Completed' :
             status === STEP_STATUS.ERROR ? 'Failed' : 'Pending'}
          </span>
          {result && result.confidence && (
            <span className="text-green-600">{result.confidence}%</span>
          )}
        </div>

        {/* Error Display - Compact */}
        {error && (
          <div className="mt-2 p-2 bg-red-100 border border-red-200 rounded text-xs text-red-700">
            <strong>Error:</strong> {typeof error === 'string' ? error.substring(0, 100) + (error.length > 100 ? '...' : '') : 'Processing failed'}
          </div>
        )}

        {/* Expanded Details - Only when requested */}
        {expanded && (result || error) && (
          <div className="mt-3 pt-3 border-t border-gray-200">
            <div className="space-y-2">
              {/* Quick Actions */}
              <div className="flex flex-wrap gap-1">
                {getActionButtons()}
              </div>

              {/* Summary Info */}
              {result && result.summary && (
                <div className="text-xs">
                  <strong className="text-gray-700">Summary:</strong>
                  <p className="text-gray-600 mt-1">{result.summary}</p>
                </div>
              )}

              {/* Detailed Error */}
              {error && (
                <div className="text-xs">
                  <strong className="text-red-700">Full Error:</strong>
                  <pre className="text-red-600 mt-1 whitespace-pre-wrap bg-red-50 p-2 rounded max-h-32 overflow-auto">
                    {typeof error === 'string' ? error : JSON.stringify(error, null, 2)}
                  </pre>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Raw Data Sections - Only when specifically requested */}
        {showRawInput && result && (
          <div className="mt-3 pt-3 border-t border-gray-200">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm font-medium text-gray-700">Raw Input Data</h4>
              <button
                onClick={() => setShowRawInput(false)}
                className="text-xs text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>
            <div className="bg-gray-50 rounded p-3 max-h-48 overflow-auto">
              <pre className="text-xs text-gray-800 whitespace-pre-wrap">
                {JSON.stringify(result.input || result.rawInput || 'No input data available', null, 2)}
              </pre>
            </div>
          </div>
        )}

        {showRawOutput && result && (
          <div className="mt-3 pt-3 border-t border-gray-200">
            <div className="flex items-center justify-between mb-2">
              <h4 className="text-sm font-medium text-gray-700">Raw Output Data</h4>
              <button
                onClick={() => setShowRawOutput(false)}
                className="text-xs text-gray-500 hover:text-gray-700"
              >
                ✕
              </button>
            </div>
            <div className="bg-gray-50 rounded p-3 max-h-48 overflow-auto">
              <pre className="text-xs text-gray-800 whitespace-pre-wrap">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          </div>
        )}
      </div>
    );
  }

  // Full detailed mode (existing implementation)
  return (
    <div className={`
      relative p-4 rounded-lg border-2 transition-all duration-200
      ${colors.bg} ${colors.border}
      ${isActive ? 'ring-2 ring-blue-500 ring-opacity-50' : ''}
      ${status === STEP_STATUS.ERROR ? 'border-red-300 bg-red-50' : ''}
    `}>
      {/* Step Header */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <span className={`text-lg ${colors.icon}`}>{step.icon}</span>
          <div>
            <h3 className={`font-medium ${colors.text}`}>{step.name}</h3>
            <p className="text-xs text-gray-600">{step.description}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <span className={`text-sm ${getStatusColor()}`}>{getStatusIcon()}</span>
          <button
            onClick={() => setExpanded(!expanded)}
            className="text-xs text-gray-500 hover:text-gray-700"
          >
            {expanded ? '▼' : '▶'}
          </button>
        </div>
      </div>

      {/* Progress Bar */}
      {status === STEP_STATUS.RUNNING && (
        <div className="mb-2">
          <div className="w-full bg-gray-200 rounded-full h-1.5">
            <div
              className={`h-1.5 rounded-full transition-all duration-300 ${colors.progress}`}
              style={{ width: `${typeof progress === 'number' ? progress : 0}%` }}
            />
          </div>
          <div className="text-xs text-gray-500 mt-1">{typeof progress === 'number' ? progress : 0}%</div>
        </div>
      )}

      {/* Timing and Status */}
      <div className="flex items-center justify-between text-xs text-gray-500 mb-2">
        <span>
          {timing > 0 ? `Completed in ${formatTiming(timing)}` :
           status === STEP_STATUS.RUNNING ? 'Processing...' :
           `Est. ${formatTiming(step.estimatedTime)}`}
        </span>
        {result && (
          <span className="text-green-600">
            {result.confidence ? `${result.confidence}% confidence` : 'Completed'}
          </span>
        )}
      </div>

      {/* Error Display */}
      {error && (
        <div className="mb-2 p-2 bg-red-100 border border-red-200 rounded text-xs text-red-700">
          <strong>Error:</strong> {typeof error === 'string' ? error : JSON.stringify(error)}
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-1 mb-2">
        {getActionButtons()}
      </div>

      {/* Expanded Details */}
      {expanded && result && (
        <div className="mt-3 pt-3 border-t border-gray-200">
          <div className="text-xs space-y-2">
            <div>
              <strong className="text-gray-700">Outputs:</strong>
              <div className="mt-1 space-y-1">
                {step.outputs.map(output => (
                  <div key={output} className="flex justify-between">
                    <span className="text-gray-600">{output}:</span>
                    <span className="text-gray-800">
                      {result[output] ? '✓' : '✗'}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {result.summary && (
              <div>
                <strong className="text-gray-700">Summary:</strong>
                <p className="text-gray-600 mt-1">{result.summary}</p>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Raw Input Section */}
      {showRawInput && result && (
        <div className="mt-3 pt-3 border-t border-gray-200">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-700">Raw Input Data</h4>
            <button
              onClick={() => setShowRawInput(false)}
              className="text-xs text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>
          <div className="bg-gray-50 rounded p-3 max-h-64 overflow-auto">
            <pre className="text-xs text-gray-800 whitespace-pre-wrap">
              {JSON.stringify(result.input || result.rawInput || 'No input data available', null, 2)}
            </pre>
          </div>
        </div>
      )}

      {/* Raw Output Section */}
      {showRawOutput && result && (
        <div className="mt-3 pt-3 border-t border-gray-200">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-700">Raw Output Data</h4>
            <button
              onClick={() => setShowRawOutput(false)}
              className="text-xs text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>
          <div className="bg-gray-50 rounded p-3 max-h-64 overflow-auto">
            <pre className="text-xs text-gray-800 whitespace-pre-wrap">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
};

export default PipelineStepCard;
