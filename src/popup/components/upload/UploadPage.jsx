import React, { useState, useCallback } from 'react';
import documentProcessingService from '../../services/DocumentProcessingService.js';
import DragDropUpload from './DragDropUpload.jsx';
import { useFileUpload } from '../../hooks/useFileUpload.js';
import EnhancedPipelineVisualization, { LAYOUT_MODES, VIEW_MODES } from '../../../components/features/pipeline/EnhancedPipelineVisualization.jsx';

// Import unified loading components
import { LoadingSpinner } from '../../../components/ui/feedback/LoadingSpinner.jsx';

function UploadPage({ context }) {
  const [error, setError] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [pipelineLayout, setPipelineLayout] = useState(LAYOUT_MODES.COMPACT);

  // Use the custom file upload hook
  const {
    uploadFiles,
    isUploading,
    progress,
    currentFile: hookCurrentFile,
    currentStage,
    errors,
    clearErrors
  } = useFileUpload({
    maxFiles: 10,
    maxSize: 10 * 1024 * 1024, // 10MB
    onError: (errorMessage) => setError(errorMessage),
    onComplete: (result) => {
      console.log('Upload completed:', result);
    }
  });

  // Handle file upload with enhanced pipeline visualization
  const handleFilesSelected = useCallback(async (files) => {
    if (!files || files.length === 0) { return; }

    setError(null);
    clearErrors();

    // Set the current file for pipeline visualization
    const file = files[0]; // Process first file
    setSelectedFile(file);

    // File processor function for the upload hook
    const fileProcessor = async (file, options = {}) => {
      const { onProgress } = options;

      try {
        // Update progress callback
        const progressCallback = (progress) => {
          onProgress?.({
            progress: progress.progress || 0,
            stage: progress.stage || 'processing'
          });
        };

        // Process file with real document processing service
        const result = await documentProcessingService.processDocument(file, progressCallback);

        if (result.success) {
          // Add processed invoice data to context
          context.addInvoice(result.data);
          return result.data;
        }
        throw new Error(result.error);

      } catch (err) {
        console.error('File processing error:', err);
        throw err;
      }
    };

    // Use the upload hook to process files
    try {
      const result = await uploadFiles(files, fileProcessor);
      if (!result.success) {
        setError(result.error || 'Upload failed');
      }
    } catch (err) {
      setError(err.message || 'Upload failed');
    }
  }, [context, uploadFiles, clearErrors]);

  // Handle error from drag drop component
  const handleUploadError = useCallback((errorMessage) => {
    setError(errorMessage);
  }, []);

  // Handle pipeline step completion
  const handleStepComplete = useCallback((result) => {
    console.log('Pipeline step completed:', result);
  }, []);

  // Handle pipeline processing state change
  const handleProcessingChange = useCallback((processing) => {
    // Update processing state if needed
  }, []);

  return (
    <div className="h-full flex">
      {/* Left Side - Upload Area */}
      <div className="w-1/2 p-6 border-r border-gray-200 flex flex-col">
        <div className="mb-6">
          <div className="flex justify-between items-center mb-2">
            <h2 className="text-xl font-semibold text-gray-900">Upload Invoices</h2>
            <div className="flex items-center space-x-1">
              <button
                onClick={() => setPipelineLayout(LAYOUT_MODES.FULL_SCREEN)}
                className="px-2 py-1 bg-purple-600 text-white rounded text-xs hover:bg-purple-700"
                title="Full Screen View"
              >
                🖥️ Full
              </button>
            </div>
          </div>
          <p className="text-gray-600 text-sm">
            Upload PDF or image files to extract invoice data using AI
          </p>
        </div>

        {/* Status Cards */}
        <div className="mb-4 grid grid-cols-2 gap-4">
          <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
            <div className="text-sm font-medium text-blue-800">Processed Files</div>
            <div className="text-lg font-semibold text-blue-900">{context.invoices?.length || 0}</div>
          </div>
          <div className="bg-green-50 p-3 rounded-lg border border-green-200">
            <div className="text-sm font-medium text-green-800">Status</div>
            <div className="text-lg font-semibold text-green-900">
              {isUploading ? 'Processing...' : 'Ready'}
            </div>
          </div>
        </div>

        {/* Upload Area */}
        <div className="flex-1 flex flex-col">
          <DragDropUpload
            onFilesSelected={handleFilesSelected}
            onError={handleUploadError}
            maxFiles={10}
            maxSize={10 * 1024 * 1024} // 10MB
            disabled={isUploading}
            className="flex-1"
          />

          {/* Processing Status */}
          {isUploading && (
            <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center space-x-2">
                <LoadingSpinner size="small" color="blue" />
                <div className="text-sm">
                  <div className="font-medium text-blue-900">
                    {hookCurrentFile ? `Processing ${hookCurrentFile}` : 'Processing...'}
                  </div>
                  <div className="text-blue-700">{currentStage || 'Preparing...'}</div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Error display */}
        {(error || errors.length > 0) && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <div className="flex items-center">
              <span className="text-red-500 mr-2">⚠️</span>
              <div className="flex-1">
                {error && <p className="text-sm text-red-700">{error}</p>}
                {errors.length > 0 && (
                  <div className="text-sm text-red-700">
                    {errors.map((err, index) => (
                      <p key={index}>{err}</p>
                    ))}
                  </div>
                )}
              </div>
            </div>
            <button
              onClick={() => {
                setError(null);
                clearErrors();
              }}
              className="mt-2 text-xs text-red-600 hover:text-red-800"
            >
              Dismiss
            </button>
          </div>
        )}

        {/* Recent uploads */}
        {context.invoices && context.invoices.length > 0 && (
          <div className="mt-6">
            <h3 className="text-sm font-medium text-gray-900 mb-3">Recent Uploads</h3>
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {context.invoices.slice(0, 2).map((invoice) => (
                <div key={invoice.id} className="p-2 bg-gray-50 rounded text-xs">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-1">
                      <span className="text-green-500">✓</span>
                      <span className="font-medium text-gray-900 truncate">{invoice.filename}</span>
                    </div>
                    <span className="text-gray-500">
                      {new Date(invoice.processedAt).toLocaleTimeString()}
                    </span>
                  </div>
                  <div className="text-gray-600 ml-4 mt-1">
                    <div>Invoice: {invoice.number || 'N/A'}</div>
                    <div>Total: {invoice.total_gross ? `${invoice.total_gross} ${invoice.currency || 'PLN'}` : 'N/A'}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Right Side - Multi-Step Pipeline */}
      <div className="w-1/2 flex flex-col">
        <EnhancedPipelineVisualization
          file={selectedFile}
          isProcessing={isUploading}
          onProcessingChange={handleProcessingChange}
          onStepComplete={handleStepComplete}
          onError={handleUploadError}
          autoRun={true}
          initialLayout={LAYOUT_MODES.COMPACT}
          initialViewMode={VIEW_MODES.COMPACT}
        />
      </div>

      {/* Full Screen Pipeline (when requested) */}
      {selectedFile && pipelineLayout === LAYOUT_MODES.FULL_SCREEN && (
        <EnhancedPipelineVisualization
          file={selectedFile}
          isProcessing={isUploading}
          onProcessingChange={handleProcessingChange}
          onStepComplete={handleStepComplete}
          onError={handleUploadError}
          autoRun={true}
          initialLayout={pipelineLayout}
        />
      )}

    </div>
  );
}

export default UploadPage;
